import os
from dotenv import load_dotenv
from flask import Flask, request, jsonify, session
import mysql.connector
from mysql.connector import pooling
import bcrypt
import uuid
from datetime import datetime

# Load environment variables from .env file
load_dotenv()

# Initialize Flask app
app = Flask(__name__)

# MySQL Connection Pool Configuration
db_config = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'Sahil@123'),  # Default to your current password if env var not set
    'database': os.getenv('DB_NAME', 'lawfort'),
    'pool_name': 'lawfort_pool',
    'pool_size': int(os.getenv('DB_POOL_SIZE', 5))
}

# Create connection pool
connection_pool = pooling.MySQLConnectionPool(**db_config)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your_secret_key')

# Function to get database connection from pool
def get_db_connection():
    return connection_pool.get_connection()

# Function to hash passwords
def hash_password(password):
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

# Function to check password
def check_password(stored_password, entered_password):
    return bcrypt.checkpw(entered_password.encode('utf-8'), stored_password)

# Function to generate session token
def generate_session_token():
    return str(uuid.uuid4())

@app.route('/register', methods=['POST'])
def register_user():
    data = request.get_json()
    
    email = data['email']
    password = data['password']
    full_name = data['full_name']
    phone = data['phone']
    bio = data['bio']
    profile_pic = data['profile_pic']
    law_specialization = data['law_specialization']
    education = data['education']
    bar_exam_status = data['bar_exam_status']
    license_number = data['license_number']
    practice_area = data['practice_area']
    location = data['location']
    years_of_experience = data['years_of_experience']
    linkedin_profile = data['linkedin_profile']
    alumni_of = data['alumni_of']
    professional_organizations = data['professional_organizations']
    
    hashed_password = hash_password(password)
    
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Insert user into Users table
        cursor.execute("""
            INSERT INTO Users (Email, Password, Role_ID, Status)
            VALUES (%s, %s, 3, 'Active')
        """, (email, hashed_password))

        # Get the User_ID of the newly created user
        user_id = cursor.lastrowid

        # Insert user profile into User_Profile table
        cursor.execute("""
            INSERT INTO User_Profile (User_ID, Full_Name, Phone, Bio, Profile_Pic, Law_Specialization, 
                                    Education, Bar_Exam_Status, License_Number, Practice_Area, Location, 
                                    Years_of_Experience, LinkedIn_Profile, Alumni_of, Professional_Organizations)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (user_id, full_name, phone, bio, profile_pic, law_specialization, education, bar_exam_status, 
              license_number, practice_area, location, years_of_experience, linkedin_profile, alumni_of, professional_organizations))

        conn.commit()
        return jsonify({'message': 'Registration successful.'}), 201
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@app.route('/login', methods=['POST'])
def login_user():
    data = request.get_json()
    
    email = data['email']
    password = data['password']

    conn = get_db_connection()
    cursor = conn.cursor(buffered=True)

    try:
        cursor.execute("SELECT User_ID, Password FROM Users WHERE Email = %s", (email,))
        user = cursor.fetchone()

        if user and check_password(user[1], password):
            # Generate session token
            session_token = generate_session_token()

            cursor.execute("""
                INSERT INTO Session (User_ID, Session_Token, Last_Active_Timestamp)
                VALUES (%s, %s, %s)
            """, (user[0], session_token, datetime.now()))

            conn.commit()
            return jsonify({'message': 'Login successful', 'session_token': session_token}), 200
        else:
            return jsonify({'error': 'Invalid credentials'}), 401
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@app.route('/logout', methods=['POST'])
def logout_user():
    data = request.get_json()
    session_token = data['session_token']

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute("DELETE FROM Session WHERE Session_Token = %s", (session_token,))
        conn.commit()
        return jsonify({'message': 'Logout successful'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()
@app.route('/request_editor_access', methods=['POST'])
def request_editor_access():
    data = request.get_json()
    user_id = data['user_id']

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute("SELECT COUNT(*) FROM Access_Request WHERE User_ID = %s AND Status = 'Pending'", (user_id,))
        count = cursor.fetchone()[0]

        if count > 0:
            return jsonify({'error': 'You already have a pending request.'}), 400

        cursor.execute("""
            INSERT INTO Access_Request (User_ID, Status) 
            VALUES (%s, 'Pending')
        """, (user_id,))

        conn.commit()
        return jsonify({'message': 'Request for editor access sent to admin.'}), 200
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()
@app.route('/admin/approve_deny_access', methods=['POST'])
def admin_approve_deny_access():
    data = request.get_json()
    request_id = data['request_id']
    action = data['action']  # 'Approve' or 'Deny'
    admin_id = data['admin_id']

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute("SELECT User_ID, Status FROM Access_Request WHERE Request_ID = %s", (request_id,))
        request = cursor.fetchone()

        if not request or request[1] != 'Pending':
            return jsonify({'error': 'Invalid or already processed request'}), 400

        if action == 'Approve':
            cursor.execute("""
                UPDATE Access_Request 
                SET Status = 'Approved', Approved_At = NOW(), Admin_ID = %s 
                WHERE Request_ID = %s
            """, (admin_id, request_id))

            cursor.execute("UPDATE Users SET Role_ID = 2 WHERE User_ID = %s", (request[0],))  # Set role to Editor

            message = 'Editor access granted.'
        else:
            cursor.execute("""
                UPDATE Access_Request 
                SET Status = 'Denied', Denied_At = NOW(), Admin_ID = %s 
                WHERE Request_ID = %s
            """, (admin_id, request_id))

            message = 'Editor access denied.'

        conn.commit()
        return jsonify({'message': message}), 200
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()
if __name__ == '__main__':
    app.run(debug=True)
