import React, { createContext, useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// Define user roles
export enum UserRole {
  USER = 'User',
  EDITOR = 'Editor',
  ADMIN = 'Admin',
}

// Define user interface
export interface User {
  id: string;
  email: string;
  fullName: string;
  username: string;
  password: string; // In a real app, this would not be stored in the client
  phoneNumber?: string;
  bio: string;
  practiceArea: string;
  organization: string;
  role: UserRole;
  profilePhoto?: string;
  lawSpecialization?: string;
  education?: string;
  barExamStatus?: 'Passed' | 'Pending' | 'Not Applicable';
  licenseNumber?: string;
  location?: string;
  yearsOfExperience?: number;
  linkedinUrl?: string;
  alumniInformation?: string;
  professionalMemberships?: string;
}

// Define auth context interface
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  login: (username: string, password: string, role: UserRole) => Promise<boolean>;
  signup: (userData: Omit<User, 'id' | 'role'>) => Promise<boolean>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
}

// Create the context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Sample users for demonstration
const dummyUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    fullName: 'Admin User',
    username: 'admin',
    password: 'admin123',
    phoneNumber: '+****************',
    bio: 'System administrator with extensive experience in legal tech platforms',
    practiceArea: 'Administration',
    organization: 'Legal Portal',
    role: UserRole.ADMIN,
    profilePhoto: 'https://i.pravatar.cc/150?img=1',
    lawSpecialization: 'Legal Technology',
    education: 'J.D., Harvard Law School',
    barExamStatus: 'Passed',
    licenseNumber: 'AB123456',
    location: 'New York, NY',
    yearsOfExperience: 12,
    linkedinUrl: 'https://linkedin.com/in/adminuser',
    alumniInformation: 'Harvard Law School, Class of 2010',
    professionalMemberships: 'American Bar Association, New York State Bar Association',
  },
  {
    id: '2',
    email: '<EMAIL>',
    fullName: 'Editor User',
    username: 'editor',
    password: 'editor123',
    phoneNumber: '+****************',
    bio: 'Content editor for the legal portal with background in legal publishing',
    practiceArea: 'Content Management',
    organization: 'Legal Portal',
    role: UserRole.EDITOR,
    profilePhoto: 'https://i.pravatar.cc/150?img=2',
    lawSpecialization: 'Legal Publishing',
    education: 'J.D., Yale Law School',
    barExamStatus: 'Passed',
    licenseNumber: 'CD789012',
    location: 'Boston, MA',
    yearsOfExperience: 8,
    linkedinUrl: 'https://linkedin.com/in/editoruser',
    alumniInformation: 'Yale Law School, Class of 2015',
    professionalMemberships: 'American Bar Association, Massachusetts Bar Association',
  },
  {
    id: '3',
    email: '<EMAIL>',
    fullName: 'Regular User',
    username: 'user',
    password: 'user123',
    phoneNumber: '+****************',
    bio: 'Corporate lawyer specializing in mergers and acquisitions',
    practiceArea: 'Corporate Law',
    organization: 'Smith & Associates',
    role: UserRole.USER,
    profilePhoto: 'https://i.pravatar.cc/150?img=3',
    lawSpecialization: 'Mergers & Acquisitions',
    education: 'J.D., Stanford Law School',
    barExamStatus: 'Passed',
    licenseNumber: 'EF345678',
    location: 'San Francisco, CA',
    yearsOfExperience: 5,
    linkedinUrl: 'https://linkedin.com/in/regularuser',
    alumniInformation: 'Stanford Law School, Class of 2018',
    professionalMemberships: 'American Bar Association, California Bar Association',
  },
];

// Create the provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>(dummyUsers);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const navigate = useNavigate();

  // Check for saved user on initial load
  useEffect(() => {
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
      setIsAuthenticated(true);
    }
  }, []);

  // Login function
  const login = async (username: string, password: string, role: UserRole): Promise<boolean> => {
    // In a real app, this would be an API call
    const foundUser = users.find(
      (u) => u.username === username && u.password === password && u.role === role
    );

    if (foundUser) {
      setUser(foundUser);
      setIsAuthenticated(true);
      localStorage.setItem('user', JSON.stringify(foundUser));
      return true;
    }
    return false;
  };

  // Signup function
  const signup = async (userData: Omit<User, 'id' | 'role'>): Promise<boolean> => {
    // Check if username or email already exists
    const userExists = users.some(
      (u) => u.username === userData.username || u.email === userData.email
    );

    if (userExists) {
      return false;
    }

    // Create new user with USER role
    const newUser: User = {
      ...userData,
      id: (users.length + 1).toString(),
      role: UserRole.USER,
    };

    // Add to users array
    setUsers([...users, newUser]);

    // Auto login after signup
    setUser(newUser);
    setIsAuthenticated(true);
    localStorage.setItem('user', JSON.stringify(newUser));

    return true;
  };

  // Logout function
  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('user');
    navigate('/login');
  };

  // Update user function
  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));

      // Update in users array
      setUsers(users.map(u => u.id === user.id ? updatedUser : u));
    }
  };

  return (
    <AuthContext.Provider value={{ user, isAuthenticated, login, signup, logout, updateUser }}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
